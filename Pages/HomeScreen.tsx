import React, { useEffect, useRef, useState, memo, useCallback } from 'react';
import { View, StyleSheet, TouchableOpacity, FlatList } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Colors, Spacing, Shadow } from '../theme';
import Screen from '../Components/Screen';
import Typography from '../Components/Typography';
import { MaterialCommunityIcons, Ionicons } from '@expo/vector-icons';
import { useAuth, useHistory } from '../context';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  withRepeat,
  interpolate,
  Easing,
  withDelay,
} from 'react-native-reanimated';
import GlowingButton from '../Components/GlowingButton';

// Define navigation types
type RootStackParamList = {
  Home: undefined;
  Scan: { fromMainNav?: boolean };
  Settings: undefined;
  SettingsSubscription: undefined;
  History: undefined;
  ScanResults: {
    imageUri: string;
    isAnonymous: boolean;
    apiResponse: any;
  };
};

type HomeScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

// Define User type
interface User {
  id: string;
  displayName?: string;
  isPremium?: boolean;
}



const AdCard = memo(({ item, handleUpgrade }: { item: typeof adCards[0], handleUpgrade: () => void }) => {
  const adCardScale = useSharedValue(1);
  const adCardOpacity = useSharedValue(1); // Keep opacity at 1

  const handleAdCardPressIn = useCallback(() => {
    adCardScale.value = withSpring(0.98);
  }, []);

  const handleAdCardPressOut = useCallback(() => {
    adCardScale.value = withSpring(1);
  }, []);

  const adCardAnimatedStyle = useAnimatedStyle(() => ({
    opacity: adCardOpacity.value,
    transform: [
      { scale: adCardScale.value },
    ],
  }));

  return (
    <Animated.View style={[adCardAnimatedStyle, { marginHorizontal: 10 }]}>
      <TouchableOpacity
        style={styles.adCard}
        onPress={() => item.type === 'premium' ? handleUpgrade() : null}
        onPressIn={handleAdCardPressIn}
        onPressOut={handleAdCardPressOut}
        activeOpacity={0.9}
      >
        <View style={styles.adContent}>
          <MaterialCommunityIcons
            name={item.icon as any}
            size={32}
            color={Colors.DarkText}
          />
          <View style={styles.adTextContainer}>
            <Typography variant="heading2" style={styles.adTitle}>
              {item.title}
            </Typography>
            <Typography variant="bodyText" style={styles.adDescription}>
              {item.description}
            </Typography>
          </View>
        </View>
        <TouchableOpacity
          style={styles.adActionButton}
          onPress={() => item.type === 'premium' ? handleUpgrade() : null}
        >
          <Typography variant="buttonText" style={styles.adActionText}>
            {item.action}
          </Typography>
        </TouchableOpacity>
      </TouchableOpacity>
    </Animated.View>
  );
});

// Ad card data
const adCards = [
  {
    id: '1',
    type: 'premium',
    title: 'Upgrade to Premium',
    description: 'Get unlimited scans, no ads, and advanced features',
    action: 'Upgrade Now',
    icon: 'star',
  },
  {
    id: '2',
    type: 'product',
    title: 'New Smartphone',
    description: 'Check out our latest smartphone with amazing features',
    action: 'Learn More',
    icon: 'cellphone',
  },
  {
    id: '3',
    type: 'product',
    title: 'Organic Skincare',
    description: 'Discover our natural skincare collection',
    action: 'Shop Now',
    icon: 'spa',
  },
];

const HomeScreen: React.FC = memo(() => {
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const { authState } = useAuth();
  const { historyState } = useHistory();
  const [currentAdIndex, setCurrentAdIndex] = useState(0);
  const flatListRef = useRef<FlatList>(null);

  // Animation values
  const fadeAnim = useSharedValue(0);
  const slideAnim = useSharedValue(50);
  const scaleAnim = useSharedValue(0.9);

  // Floating animations for stats cards with staggered timing
  const floatAnim1 = useSharedValue(0);
  const floatAnim2 = useSharedValue(0);
  const floatAnim3 = useSharedValue(0);

  // Ad card animation values

  const adCardOpacity = useSharedValue(0);

  // Background floating elements animation values
  const bgFloat1 = useSharedValue(0);
  const bgFloat2 = useSharedValue(0);
  const bgFloat3 = useSharedValue(0);
  const bgFloat4 = useSharedValue(0);
  const bgRotate1 = useSharedValue(0);
  const bgRotate2 = useSharedValue(0);

  // Recent activity cards animation values
  const recentCardScale = useSharedValue(1);
  const recentCard1Opacity = useSharedValue(0);
  const recentCard2Opacity = useSharedValue(0);

  // Start animations on mount
  useEffect(() => {
    fadeAnim.value = withTiming(1, { duration: 800 });
    slideAnim.value = withTiming(0, { duration: 800 });
    scaleAnim.value = withSpring(1);

    // Start floating animations with staggered delays
    floatAnim1.value = withDelay(
      0,
      withRepeat(
        withTiming(1, { duration: 4000, easing: Easing.inOut(Easing.sin) }),
        -1,
        true
      )
    );

    floatAnim2.value = withDelay(
      800,
      withRepeat(
        withTiming(1, { duration: 4200, easing: Easing.inOut(Easing.sin) }),
        -1,
        true
      )
    );

    floatAnim3.value = withDelay(
      1600,
      withRepeat(
        withTiming(1, { duration: 3800, easing: Easing.inOut(Easing.sin) }),
        -1,
        true
      )
    );

    // Ad card entrance animation
    adCardOpacity.value = withDelay(600, withTiming(1, { duration: 800 }));

    // Background floating elements animations
    bgFloat1.value = withRepeat(
      withTiming(1, { duration: 8000, easing: Easing.inOut(Easing.sin) }),
      -1,
      true
    );

    bgFloat2.value = withDelay(
      2000,
      withRepeat(
        withTiming(1, { duration: 12000, easing: Easing.inOut(Easing.sin) }),
        -1,
        true
      )
    );

    bgFloat3.value = withDelay(
      4000,
      withRepeat(
        withTiming(1, { duration: 10000, easing: Easing.inOut(Easing.sin) }),
        -1,
        true
      )
    );

    bgFloat4.value = withDelay(
      1000,
      withRepeat(
        withTiming(1, { duration: 15000, easing: Easing.inOut(Easing.sin) }),
        -1,
        true
      )
    );

    // Background rotation animations
    bgRotate1.value = withRepeat(
      withTiming(360, { duration: 20000, easing: Easing.linear }),
      -1,
      false
    );

    bgRotate2.value = withRepeat(
      withTiming(-360, { duration: 25000, easing: Easing.linear }),
      -1,
      false
    );

    // Recent activity cards entrance animations
    recentCard1Opacity.value = withDelay(1000, withTiming(1, { duration: 600 }));
    recentCard2Opacity.value = withDelay(1200, withTiming(1, { duration: 600 }));
  }, []);

  // Auto-scroll ads
  useEffect(() => {
    const interval = setInterval(() => {
      const nextIndex = (currentAdIndex + 1) % adCards.length;
      setCurrentAdIndex(nextIndex);
      flatListRef.current?.scrollToIndex({
        index: nextIndex,
        animated: true,
        viewPosition: 0.5, // Center the item
      });
    }, 5000);

    return () => clearInterval(interval);
  }, [currentAdIndex]);

  // Animated styles
  const headerStyle = useAnimatedStyle(() => ({
    opacity: fadeAnim.value,
    transform: [
      { translateY: slideAnim.value },
      { scale: scaleAnim.value },
    ] as any,
  }));

  // Floating animation styles for stats cards
  const floatingStyle1 = useAnimatedStyle(() => ({
    transform: [
      { translateY: interpolate(floatAnim1.value, [0, 1], [0, -4]) },
    ],
  }));

  const floatingStyle2 = useAnimatedStyle(() => ({
    transform: [
      { translateY: interpolate(floatAnim2.value, [0, 1], [0, -3]) },
    ],
  }));

  const floatingStyle3 = useAnimatedStyle(() => ({
    transform: [
      { translateY: interpolate(floatAnim3.value, [0, 1], [0, -5]) },
    ],
  }));

  // Ad card animated styles


  // Background floating elements animated styles
  const bgElement1Style = useAnimatedStyle(() => ({
    transform: [
      { translateX: interpolate(bgFloat1.value, [0, 1], [0, 30]) },
      { translateY: interpolate(bgFloat1.value, [0, 1], [0, -20]) },
      { rotate: `${bgRotate1.value}deg` },
    ] as any,
  }));

  const bgElement2Style = useAnimatedStyle(() => ({
    transform: [
      { translateX: interpolate(bgFloat2.value, [0, 1], [0, -25]) },
      { translateY: interpolate(bgFloat2.value, [0, 1], [0, 35]) },
    ] as any,
  }));

  const bgElement3Style = useAnimatedStyle(() => ({
    transform: [
      { translateX: interpolate(bgFloat3.value, [0, 1], [0, 20]) },
      { translateY: interpolate(bgFloat3.value, [0, 1], [0, -30]) },
      { rotate: `${bgRotate2.value}deg` },
    ] as any,
  }));

  const bgElement4Style = useAnimatedStyle(() => ({
    transform: [
      { translateX: interpolate(bgFloat4.value, [0, 1], [0, -15]) },
      { translateY: interpolate(bgFloat4.value, [0, 1], [0, 25]) },
    ] as any,
  }));

  // Recent activity cards animated styles
  const recentCardAnimatedStyle = useAnimatedStyle(() => ({
    transform: [
      { scale: recentCardScale.value },
    ] as any,
  }));

  const recentCard1Style = useAnimatedStyle(() => ({
    opacity: recentCard1Opacity.value,
  }));

  const recentCard2Style = useAnimatedStyle(() => ({
    opacity: recentCard2Opacity.value,
  }));



  const handleScan = useCallback(() => {
    console.log('Scan button pressed');

    // Navigate to scan screen with fromMainNav parameter
    try {
      navigation.navigate('Scan', { fromMainNav: true });
    } catch (error) {
      console.error('Navigation error:', error);
    }
  }, [navigation]);

  const handleSettings = useCallback(() => {
    navigation.navigate('Settings');
  }, [navigation]);

  const handleUpgrade = useCallback(() => {
    navigation.navigate('SettingsSubscription');
  }, [navigation]);

  // Ad card press handlers


  // Recent activity card press handlers
  const handleRecentCardPressIn = useCallback(() => {
    recentCardScale.value = withSpring(0.97);
  }, []);

  const handleRecentCardPressOut = useCallback(() => {
    recentCardScale.value = withSpring(1);
  }, []);

  const renderAdCard = useCallback(({ item }: { item: typeof adCards[0] }) => (
    <AdCard item={item} handleUpgrade={handleUpgrade} />
  ), [handleUpgrade]);

  return (
    <Screen style={styles.container}>
      {/* Background Floating Elements */}
      <Animated.View style={[styles.bgElement1, bgElement1Style]} />
      <Animated.View style={[styles.bgElement2, bgElement2Style]} />
      <Animated.View style={[styles.bgElement3, bgElement3Style]} />
      <Animated.View style={[styles.bgElement4, bgElement4Style]} />

      {/* Header */}
      <Animated.View style={[styles.header, headerStyle]}>
        <View style={styles.welcomeContainer}>
          <Typography variant="heading1" style={styles.welcomeText}>
            Welcome{(authState.user as User)?.displayName ? `, ${(authState.user as User).displayName}` : ''}
          </Typography>
          <Typography variant="bodyText" style={styles.subtitle}>
            Let's check what's in your products
          </Typography>
        </View>
        <TouchableOpacity onPress={handleSettings} style={styles.settingsButton}>
          <MaterialCommunityIcons
            name="cog"
            size={24}
            color={Colors.DarkText}
          />
        </TouchableOpacity>
      </Animated.View>

      {/* Quick Stats */}
      <View style={styles.statsContainer}>
        <Animated.View style={[styles.statItem, floatingStyle1]}>
          <Typography variant="heading2" style={styles.statNumber}>
            {historyState.scans.length}
          </Typography>
          <Typography variant="description" style={styles.statLabel}>
            Scans
          </Typography>
        </Animated.View>
        <Animated.View style={[styles.statItem, floatingStyle2]}>
          <Typography variant="heading2" style={styles.statNumber}>
            {(authState.user as User)?.isPremium ? 'Premium' : 'Free'}
          </Typography>
          <Typography variant="description" style={styles.statLabel}>
            Plan
          </Typography>
        </Animated.View>
        <Animated.View style={[styles.statItem, floatingStyle3]}>
          <Typography variant="heading2" style={styles.statNumber}>
            {historyState.scans.filter(item => item.grade === 'A' || item.grade === 'B').length}
          </Typography>
          <Typography variant="description" style={styles.statLabel}>
            Safe Items
          </Typography>
        </Animated.View>
      </View>

      {/* Ad Carousel */}
      <View style={styles.adCarouselContainer}>
        <FlatList
          ref={flatListRef}
          data={adCards}
          renderItem={renderAdCard}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          snapToInterval={300}
          decelerationRate="fast"
          contentContainerStyle={styles.adCarouselContent}
          contentInsetAdjustmentBehavior="automatic"
          onMomentumScrollEnd={(event) => {
            const newIndex = Math.round(
              event.nativeEvent.contentOffset.x / 300
            );
            setCurrentAdIndex(newIndex);
          }}
        />
        {/* Pagination Dots */}
        <View style={styles.paginationContainer}>
          {adCards.map((_, index) => (
            <View
              key={index}
              style={[
                styles.paginationDot,
                index === currentAdIndex && styles.paginationDotActive,
              ]}
            />
          ))}
        </View>
      </View>

      {/* Recent Activity */}
      <View style={styles.recentActivityContainer}>
        <View style={styles.recentActivityHeader}>
          <Typography variant="heading2" style={styles.recentActivityTitle}>
            Recent Activity
          </Typography>
          <TouchableOpacity onPress={() => navigation.navigate('History')}>
            <Typography variant="bodyText" style={styles.seeAllText}>
              See All
            </Typography>
          </TouchableOpacity>
        </View>
        {historyState.scans.slice(0, 2).map((item, index) => (
          <Animated.View
            key={item.id}
            style={[
              recentCardAnimatedStyle,
              index === 0 ? recentCard1Style : recentCard2Style,
            ]}
          >
            <TouchableOpacity
              style={styles.recentItem}
              onPress={() =>
                navigation.navigate('ScanResults', {
                  imageUri: item.imageUri,
                  isAnonymous: false,
                  fromHistory: true,  // Mark that we're navigating from history
                  apiResponse: {
                    rating: item.grade,
                    summary: item.summary,
                    product_details: {
                      name: item.productName,
                      ingredients: [...item.goodIngredients.map(i => i.name), ...item.badIngredients.map(i => i.name)]
                    },
                    good_ingredients: item.goodIngredients.map(i => ({
                      name: i.name,
                      description: i.description
                    })),
                    harmful_ingredient_analysis: item.badIngredients.map(i => ({
                      ingredient: i.name,
                      impact: i.description
                    })),
                    explanation: {
                      influencing_ingredients: item.badIngredients.map(i => i.name),
                      rationale: item.explanation
                    },
                    effects: item.effects || {
                      positive_effects: { short_term: [], long_term: [] },
                      negative_effects: { short_term: [], long_term: [] }
                    },
                    info_cards: item.infoCards || [],
                    detailed_rationale: {
                      overall_assessment: item.summary
                    }
                  }
                })
              }
              onPressIn={handleRecentCardPressIn}
              onPressOut={handleRecentCardPressOut}
              activeOpacity={0.9}
            >
              <View style={styles.recentItemContent}>
                <Typography variant="bodyText" style={styles.recentItemName}>
                  {item.productName}
                </Typography>
                <Typography variant="description" style={styles.recentItemDate}>
                  {new Date(item.timestamp).toLocaleDateString()}
                </Typography>
              </View>
              <View
                style={[
                  styles.gradeIndicator,
                  { backgroundColor: getGradeColor(item.grade) },
                ]}
              >
                <Typography variant="buttonText" style={styles.gradeText}>
                  {item.grade}
                </Typography>
              </View>
            </TouchableOpacity>
          </Animated.View>
        ))}
      </View>

      {/* Floating Scan Button with Glow Effect - Simplified for stability */}
      <View style={styles.scanButtonContainer}>
        {/* Scan button with original glow effect */}
        <GlowingButton
          title="SCAN PRODUCT"
          onPress={handleScan}
          variant="primary"
          style={styles.scanButton}
          glowIntensity={0.6}
          alwaysGlow={true}
          glowColor={Colors.Success}
          icon={
            <View style={styles.iconWrapper}>
              <Ionicons name="camera" size={24} color={Colors.BackgroundPrimary} />
            </View>
          }
        />
      </View>
    </Screen>
  );
});

// Helper function for grade colors
const getGradeColor = (grade: string) => {
  switch (grade) {
    case 'A':
      return Colors.GradeA;
    case 'B':
      return Colors.GradeB;
    case 'C':
      return Colors.GradeC;
    case 'D':
      return Colors.GradeD;
    case 'E':
      return Colors.GradeE;
    default:
      return Colors.LightText;
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.BackgroundPrimary,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing.Large,
    paddingTop: Spacing.Large,
  },
  welcomeContainer: {
    flex: 1,
  },
  welcomeText: {
    color: Colors.DarkText,
  },
  subtitle: {
    color: Colors.LightText,
    marginTop: Spacing.Small,
  },
  settingsButton: {
    padding: Spacing.Small,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.Large,
    marginTop: Spacing.Medium,
  },
  statItem: {
    alignItems: 'center',
    backgroundColor: Colors.BackgroundPrimary,
    paddingVertical: Spacing.Medium,
    paddingHorizontal: Spacing.Large,
    borderRadius: 16,
    minWidth: 80,
    ...Shadow.Medium,
  },
  statNumber: {
    color: Colors.DarkText,
  },
  statLabel: {
    color: Colors.LightText,
    marginTop: Spacing.Small,
  },
  adCarouselContainer: {
    marginTop: Spacing.Small,
    overflow: 'visible',
  },
  adCarouselContent: {
    paddingHorizontal: Spacing.Large,
    paddingVertical: Spacing.Medium, // Add vertical padding for shadows
    justifyContent: 'center',
    alignItems: 'center',
  },
  adCard: {
    backgroundColor: Colors.BackgroundPrimary,
    borderRadius: 16,
    padding: Spacing.Large,
    width: 280, // Fixed width for uniform cards
    ...Shadow.Medium,
  },
  adContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  adTextContainer: {
    marginLeft: Spacing.Medium,
    flex: 1,
    minHeight: 60, // Ensure consistent height
  },
  adTitle: {
    color: Colors.DarkText,
  },
  adDescription: {
    color: Colors.LightText,
    marginTop: Spacing.Small,
  },
  adActionButton: {
    backgroundColor: Colors.DarkText,
    paddingVertical: Spacing.Medium,
    paddingHorizontal: Spacing.Large,
    borderRadius: 8,
    marginTop: Spacing.Medium,
    alignItems: 'center',
    minHeight: 44, // Ensure consistent button height
  },
  adActionText: {
    color: Colors.BackgroundPrimary,
  },
  paginationContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: Spacing.Small,
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.SurfaceSecondary,
    marginHorizontal: 4,
  },
  paginationDotActive: {
    backgroundColor: Colors.DarkText,
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  recentActivityContainer: {
    flex: 1,
    paddingHorizontal: Spacing.Large,
    marginTop: Spacing.Medium,
    paddingBottom: 80, // Reduced padding at the bottom to account for the scan button
  },
  recentActivityHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.Small,
  },
  recentActivityTitle: {
    color: Colors.DarkText,
  },
  seeAllText: {
    color: Colors.AccentBlue,
  },
  recentItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: Colors.BackgroundPrimary,
    padding: Spacing.Small,
    borderRadius: 16,
    marginBottom: Spacing.Small,
    ...Shadow.Small,
  },
  recentItemContent: {
    flex: 1,
  },
  recentItemName: {
    color: Colors.DarkText,
  },
  recentItemDate: {
    color: Colors.LightText,
    marginTop: Spacing.Small,
  },
  gradeIndicator: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: Spacing.Medium,
  },
  gradeText: {
    color: Colors.BackgroundPrimary,
  },
  scanButtonContainer: {
    position: 'absolute',
    bottom: 16, // Lower positioning to prevent overlap
    left: 0,
    right: 0,
    alignItems: 'center',
    zIndex: 1000,
  },
  scanButton: {
    width: 240, // Slightly wider
    height: 60, // Slightly taller
    borderRadius: 30, // Adjusted for new height
    elevation: 8, // Android shadow
    shadowColor: Colors.Success, // iOS shadow with green color
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  iconWrapper: {
    marginRight: Spacing.Small,
  },

  // Background floating elements
  bgElement1: {
    position: 'absolute',
    top: 100,
    left: 50,
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: Colors.SurfaceSecondary,
    opacity: 0.03,
    zIndex: -1,
  },
  bgElement2: {
    position: 'absolute',
    top: 200,
    right: 30,
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: Colors.LightText,
    opacity: 0.04,
    zIndex: -1,
  },
  bgElement3: {
    position: 'absolute',
    top: 350,
    left: 20,
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: Colors.SurfaceSecondary,
    opacity: 0.03,
    zIndex: -1,
  },
  bgElement4: {
    position: 'absolute',
    top: 500,
    right: 60,
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: Colors.LightText,
    opacity: 0.05,
    zIndex: -1,
  },

});

export default HomeScreen;